<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars'); ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard'); ?>">Fee Dashboard</a></li>
  <li>Fees Collection Summary Date Range</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <div class="d-flex justify-content-between" style="width:100%;">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a>
              Fees Collection Summary Date Range
            </h3>
            <label class="checkbox-inline" style="margin-left: 20px;"><input style="width:20px;height: 20px;" type="checkbox" name="show_date_wise_summary" id="show_date_wise_summary"><span style="font-size:16px; margin-left: 10px;">Show Date Wise Summary</span></label>
          </div>
        </div>
      </div>
    </div>

      <div class="card-body" id="printArea">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-3 form-group">
                    <label><span style="color: #1e428a; font-weight: 700;" id="reconLable"></span> Date Range</label>
                    <div id="reportrange" class="dtrange" style="width: 100%">
                        <span></span>
                        <input type="hidden" id="from_date">
                        <input type="hidden" id="to_date">
                    </div>
                </div>
                <div class="col-md-3 form-group">
                      <label>Fee Type</label>
                      <select class="form-control select" multiple title='All' id="fee_type" name="fee_type">
                       
                        <?php foreach ($fee_blueprints as $key => $val) { ?>
                          <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
                        <?php } ?>
                        <?php if (isset($sales) && $sales) {
                          echo '<option value="sales">Sales</option>';
                        } ?>
                        <?php if (isset($admission) && $admission) {
                          echo '<option value="application">Applications</option>';
                        } ?>
                        <?php if ($additionalAmount) {
                          echo '<option value="excess_amount">Excess Amount</option>';
                        } ?>
                      </select>
                </div>
                <div class="col-md-2 form-group" style="margin-top: 25px;">
                    <input type="button" name="search" onclick="search_day_wise()" id="search_day_wise" class="btn btn-primary" value="Get Report">
                </div>
            </div>

            <?php if ($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
              <div class="col-md-2 report-controls-wrapper" style="margin-left:-20px;">
                <div class="form-group">
                  <label class="control-label" style="margin-bottom: 6px;">Saved Reports</label>
                  <div class="report-row">
                    <select name="" id="filter_types" class="form-control grow-select" onchange="selectFilters()">
                      <option value="">Select Report</option>
                    </select>

                    <?php if ($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
                      <div class="dc-buttons">
                        <input type="button" name="reload" id="reload_filter" style="border-radius: 8px !important;" class="btn btn-info" value="Reload" onclick="selectFilters()">
                        <input type="button" name="save" id="save_filter" style="border-radius: 8px !important;" class="btn btn-info" value="Save">
                        <input type="button" name="update" id="update_filter" style="border-radius: 8px !important;" class="btn btn-info" value="Update">
                      </div>
                  </div>
                </div>
                <style>
                  .report-row {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                  }

                  .grow-select {
                    flex: 1.3;
                    min-width: 200px;
                  }

                  .dc-buttons {
                    display: flex;
                    gap: 10px;
                    flex-shrink: 0;
                  }
                </style>
                <script>
                  $(document).ready(function() {
                  get_predefined_filters();

                  $('#save_filter').on('click', function() {
                    saveFilter();
                  });

                  $('#update_filter').on('click', function() {
                    updateFilter();
                  });
                });

                function get_predefined_filters() {
                  return $.ajax({
                    url: '<?php echo site_url('feesv2/reports/get_predefined_filters5'); ?>',
                    type: 'POST',
                    success: function(data) {
                      try {
                        var res_data = JSON.parse(data);

                        if (Array.isArray(res_data) && res_data.length > 0) {
                          var html = '<option value="">Select</option>';
                          res_data.forEach(filter => {
                            html += `<option value="${filter.id}">${filter.title}</option>`;
                          });

                          $('#filter_types').html(html);
                        } else {
                          console.warn("No predefined filters found.");
                          $('#filter_types').html('<option value="">No Filters Available</option>');
                        }
                      } catch (error) {
                        console.error("Error parsing response:", error);
                        $('#filter_types').html('<option value="">Error Loading Filters</option>');
                      }
                    },
                    error: function(xhr, status, error) {
                      console.error("AJAX Error:", error);
                      $('#filter_types').html('<option value="">Error Fetching Filters</option>');
                    }
                  });
                }

                function saveFilter() {
                  bootbox.prompt({
                    inputType: 'text',
                    placeholder: 'Enter the Title name',
                    title: "Save filters",
                    className: 'half-width-box',
                    buttons: {
                      confirm: {
                        label: 'Yes',
                        className: 'btn-success'
                      },
                      cancel: {
                        label: 'No',
                        className: 'btn-danger'
                      }
                    },
                    callback: function(remarks) {
                      if (remarks === null) return;

                      $('.bootbox .error-message').remove();
                      remarks = remarks.trim();

                      if (!remarks) {
                        new PNotify({
                          title: 'Missing Title',
                          text: 'Please enter a name to save the filter.',
                          type: 'error',
                          addclass: 'custom-pnotify half-width-notify',
                          cornerclass: '',
                          animate: {
                            animate: true,
                            in_class: 'fadeInRight',
                            out_class: 'fadeOutRight'
                          },
                          styling: 'bootstrap3',
                          delay: 3000
                        });
                        return false;
                      }

                      if (remarks.length < 5 || remarks.length > 50) {
                        setTimeout(() => {
                          $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                        }, 10);
                        return false;
                      }

                      let duplicate = false;
                      $('#filter_types option').each(function() {
                        if ($(this).text().trim().toLowerCase() === remarks.toLowerCase()) {
                          duplicate = true;
                          return false;
                        }
                      });

                      if (duplicate) {
                        setTimeout(() => {
                          $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">A filter with this name already exists.</div>`);
                        }, 10);
                        return false;
                      }

                      // Save filters including show_date_wise_summary checkbox
                      let filterData = {
                        title: remarks,
                        from_date: $('#from_date').val(),
                        to_date: $('#to_date').val(),
                        fee_type: $('#fee_type').val(),
                        show_date_wise_summary: $('#show_date_wise_summary').is(':checked') ? 1 : 0
                      };

                      $.ajax({
                        url: '<?php echo site_url('feesv2/reports/save_filters5'); ?>',
                        type: 'POST',
                        data: filterData,
                        success: function(data) {
                          if (data) {
                            let lastId = 0;
                            $('#filter_types option').each(function() {
                              const val = parseInt($(this).val());
                              if (!isNaN(val) && val > lastId) lastId = val;
                            });

                            const newId = lastId + 1;

                            $('#filter_types').append(
                              $('<option>', {
                                value: newId,
                                text: remarks
                              })
                            );

                            $('#filter_types').val(newId).trigger('change');

                            new PNotify({
                              title: 'Success',
                              text: 'Filter Saved Successfully',
                              type: 'success',
                              addclass: 'custom-pnotify half-width-notify'
                            });
                          } else {
                            new PNotify({
                              title: 'Error',
                              text: 'Something went wrong',
                              type: 'error',
                              addclass: 'custom-pnotify half-width-notify'
                            });
                          }
                        }
                      });
                    }
                  });

                  setTimeout(() => {
                    $('.bootbox .modal-dialog').css({
                      'max-width': '400px',
                      'margin': '1.75rem auto'
                    });

                    $('.bootbox-input').on('input', function() {
                      const inputVal = $(this).val().trim();
                      if (inputVal.length > 50) {
                        $(this).val(inputVal.slice(0, 50));
                        $('.bootbox .error-message').remove();
                        $(this).after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                      } else {
                        $('.bootbox .error-message').remove();
                      }
                    });
                  }, 10);
                }

                function collectFilterData() {
                  return {
                    from_date: $('#from_date').val(),
                    to_date: $('#to_date').val(),
                    fee_type: $('#fee_type').val(),
                    show_date_wise_summary: $('#show_date_wise_summary').is(':checked') ? 1 : 0
                  };
                }

                function updateFilter() {
                  const selectedFilterId = $('#filter_types').val();

                  if (!selectedFilterId) {
                    bootbox.alert({
                      title: "No Filter Selected",
                      message: "Please select a filter to update.",
                      className: "half-width-box",
                      buttons: {
                        ok: {
                          label: 'OK',
                          className: 'btn-primary'
                        }
                      }
                    });

                    // Center the alert modal
                    setTimeout(() => {
                      $('.bootbox .modal-dialog').css({
                        'max-width': '400px',
                        'margin': '1.75rem auto'
                      });
                    }, 10);

                    return; // stop the update function
                  }

                  let filterData = collectFilterData();
                  filterData.filter_types_id = selectedFilterId;
                  filterData.title = $('#filter_types option:selected').text().trim();
                  filterData.stakeholder_id = $('#stakeholder_id').val();

                  bootbox.confirm({
                    title: "Update Filters",
                    message: 'Are you sure you want to update the filter?',
                    className: "half-width-box",
                    buttons: {
                      confirm: {
                        label: 'Yes',
                        className: 'btn-success'
                      },
                      cancel: {
                        label: 'No',
                        className: 'btn-danger'
                      }
                    },
                    callback: function(result) {
                      if (result) {
                        $.ajax({
                          url: '<?php echo site_url('feesv2/reports/update_filters5'); ?>',
                          type: 'POST',
                          data: filterData,
                          complete: function() {
                            $.when(get_predefined_filters()).done(function() {
                              if (
                                $('#filter_types option[value="' + filterData.filter_types_id + '"]').length === 0
                              ) {
                                $('#filter_types').append(
                                  $('<option>', {
                                    value: filterData.filter_types_id,
                                    text: filterData.title
                                  })
                                );
                              }

                              $('#filter_types').val(filterData.filter_types_id);
                              selectFilters();

                              new PNotify({
                                title: 'Success',
                                text: 'Filter updated successfully.',
                                type: 'success',
                                addclass: 'custom-pnotify half-width-notify'
                              });
                            });
                          }
                        });
                      }
                    }
                  });

                  setTimeout(() => {
                    $('.bootbox .modal-dialog').css({
                      'max-width': '400px',
                      'margin': '1.75rem auto'
                    });
                  }, 10);
                }

                                function selectFilters() {
                  const filterId = $('#filter_types').val();

                  if (!filterId || filterId.trim() === '') {
                    $('#reload_filter').prop('disabled', true);
                    return;
                  } else {
                    $('#reload_filter').prop('disabled', false);
                  }

                  $('#reload_filter').show();

                  $.ajax({
                    url: '<?php echo site_url('feesv2/reports/get_predefined_filters_by_id5'); ?>',
                    type: 'POST',
                    data: {
                      filter_id: filterId
                    },
                    dataType: 'json',
                    success: function(response) {
                      if (response && response.success && response.filters_selected) {
                        const filters = response.filters_selected;

                        // Set date range
                        if (filters.from_date && filters.to_date) {
                          $('#from_date').val(filters.from_date);
                          $('#to_date').val(filters.to_date);
                          $('#reportrange span').html(
                            moment(filters.from_date, 'DD-MM-YYYY').format("MMM D, YYYY") +
                            ' - ' +
                            moment(filters.to_date, 'DD-MM-YYYY').format("MMM D, YYYY")
                          );
                        }

                        // Set fee type (multi-select)
                        const updateSelect = (selector, values) => {
                          if (values !== undefined && values !== null) {
                            if (typeof values === 'string') values = values.split(',');
                            $(selector).val(values);
                            $(selector).selectpicker?.('refresh');
                          }
                        };
                        updateSelect('#fee_type', filters.fee_type);

                        // Set show_date_wise_summary checkbox
                        if (filters.show_date_wise_summary !== undefined) {
                          $('#show_date_wise_summary').prop('checked', filters.show_date_wise_summary == 1);
                        }

                        // Automatically trigger the report
                        search_day_wise();

                      } else {
                        alert('Failed to fetch filter details. Please try again.');
                      }
                    }
                  });
                }
                </script>
              <?php } ?>
              </div>
            <?php } ?>

            
            <div class="col-sm-12 col-md-12">
              <div style="display: none;" class="progress" id="progress">
                <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%;"></div>
              </div>
            </div>

            <div id="printArea_summary" >
                <div id="print_summary" style="display: none;text-align: center;margin: 32px 0;">
                  <h3>
                    Fee Day Wise Summary Report
                  </h3>
                  <h5>
                    From <span id="fromDate_summary"></span> To <span id="toDate_summary"></span>
                  </h5>
                </div>

                <div style="clear: both"></div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="panel-body day_wise_fee_summary table-responsive" style="padding: 0" id="daybookFeeSummary">
                        </div>
                        <div class="col-12 text-center loading-icon" style="display: none;">
                            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
      </div>
    </div>
  </div>

  <script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
  <script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>


  <script>
    $(document).on('click', function(event) {
      var $target = $(event.target);
      if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
        $('.bootstrap-select').removeClass('open show');
        $('.dropdown-menu').removeClass('show');
      }
    });
    $("#reportrange").daterangepicker({
        ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment().endOf('month')],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'MM.DD.YYYY',
        separator: ' to ',
        startDate: moment(),
        endDate: moment()
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });
    $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

    $('#from_date').val(moment().format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));


    function search_day_wise(){
        // Clear previous data
        $('#daybookFeeSummary').empty();

        // Show loading icon
        $('.loading-icon').show();

        var $btn = $("#search_day_wise");
        var originalText = $btn.val();
        $btn.prop('disabled', true).val('Please wait...');

        // Get date values from the form
        var from_date = $('#from_date').val();
        var to_date = $('#to_date').val();
        var fee_type = $('#fee_type').val();
        var show_date_wise_summary = $('#show_date_wise_summary').is(':checked') ? 1 : 0;

        // Set the date values for the print summary
        $('#fromDate_summary').text($('#from_date').val());
        $('#toDate_summary').text($('#to_date').val());
        $('#print_summary').show();
        $("#progress").show();

        $.ajax({
            url: '<?php echo site_url('feesv2/reports_v2/get_fee_day_wise_summary'); ?>',
            type: 'post',
            data: {'from_date':from_date, 'to_date':to_date, 'fee_type': fee_type, 'show_date_wise_summary': show_date_wise_summary},
            beforeSend: function() {
                // Ensure loading icon is visible
                $('.loading-icon').show();
            },
            success: function(data) {
                // Hide loading icon
                $('.loading-icon').hide();
                $("#progress").hide();
                $btn.prop('disabled', false).val(originalText);
                console.log(data); // Debug: Log the response data
                var res_data = JSON.parse(data);
                var show_date_wise = $('#show_date_wise_summary').is(':checked');

                if (show_date_wise) {
                    construct_date_wise_summary_data(res_data);
                } else {
                    construct_fee_day_wise_summary_data(res_data);
                }
            },
            error: function(xhr, status, error) {
                $('.loading-icon').hide();
                $("#progress").hide();
                $btn.prop('disabled', false).val(originalText);
                console.error('AJAX Error:', status, error);
                alert('Error fetching data. Please try again.');
            }
        });
    }

    function construct_fee_day_wise_summary_data(res_data){
        // Destroy existing DataTable if it exists
        if ($.fn.DataTable.isDataTable('#fee_summary_table')) {
            $('#fee_summary_table').DataTable().destroy();
        }

        var html ='Note: This report includes calculation of Excess Amount (Debited).';
        html +='<table id="fee_summary_table" class="table table-bordered">';
        html +='<thead>';
        html +='<tr class="bg-primary text-white">';
        html +='<th>#</th>';
        html +='<th>Fee Type</th>';
        html +='<th>Concession Amount</th>';
        html +='<th>Fine Amount</th>';
        html +='<th>Collected Amount</th>';
        html +='</tr>';
        html +='</thead>';
        html +='<tbody>';

        var total_collected = 0;
        var total_concession = 0;
        var total_fine = 0;

        if(res_data && res_data.length > 0) {
            $.each(res_data, function(index, item) {
                html +='<tr>';
                html +='<td>' + (index + 1) + '</td>';
                html +='<td>' + (item.fee_type || 'N/A') + '</td>';
                html +='<td>' + (parseFloat(item.concession_amount) || 0).toFixed(0) + '</td>';
                html +='<td>' + (parseFloat(item.fine_amount) || 0).toFixed(0) + '</td>';
                html +='<td>' + (parseFloat(item.amount_paid + item.fine_amount) || 0).toFixed(0) + '</td>';
                html +='</tr>';

                // Add to totals
                total_collected += parseFloat(item.amount_paid + item.fine_amount) || 0;
                total_concession += parseFloat(item.concession_amount) || 0;
                total_fine += parseFloat(item.fine_amount) || 0;
            });
        } else {
            html +='<tr><td colspan="4" class="text-center">No data found</td></tr>';
        }

        html +='</tbody>';
        html +='<tfoot>';
        // Add total row
        html +='<tr class="bg-light font-weight-bold">';
        html +='<th colspan="2"></th>';
        html +='<th>' + total_concession.toFixed(0) + '</th>';
        html +='<th>' + total_fine.toFixed(0) + '</th>';
        html +='<th>' + total_collected.toFixed(0) + '</th>';
        html +='</tr>';
        html +='</tfoot>';
        html +='</table>';

        // Set the HTML content
        $('#daybookFeeSummary').html(html);

        // Initialize DataTable with export buttons
        $('#fee_summary_table').DataTable({
            dom: 'Bfrtip',
            paging: false,
            searching: false,
            ordering: false,
            info: false,
            buttons: [
                {
                  extend: 'print',
                  text: '<button class="btn btn-info" style="border-radius: 8px !important;"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
                  title: 'Fee Day Wise Summary Report',
                  messageTop: function() {
                    return '<div style="text-align: center; margin-bottom: 20px;">' +
                           '<h3>Fee Day Wise Summary Report</h3>' +
                           '<h5>From ' + $('#from_date').val() + ' To ' + $('#to_date').val() + '</h5>' +
                           '</div>';
                  },
                  footer: true,
                  exportOptions: {
                    columns: ':visible',
                  },
                  customize: function (win) {
                    $(win.document.head).append(`
                      <style>
                        @page {
                          size: auto;
                          margin: 12mm;
                        }
                        .dt-print-view h3, .dt-print-view h5 {
                          margin: 10px 0;
                        }
                      </style>
                    `);
                  },
                },
                {
                    extend: 'excel',
                    text: '<button class="btn btn-info" style="border-radius: 8px !important;"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
                    filename: 'Fee_Day_Wise_Summary_Report',
                    title: 'Fee Day Wise Summary Report',
                    messageTop: 'From ' + $('#from_date').val() + ' To ' + $('#to_date').val(),
                    footer: true
                },

            ]
        });
    }



    function construct_date_wise_summary_data(res_data){
        // Destroy existing DataTable if it exists
        if ($.fn.DataTable.isDataTable('#fee_summary_table')) {
            $('#fee_summary_table').DataTable().destroy();
        }

        var html = '';

        // Get payment modes from response
        var paymentModes = res_data.paymentModes || [];
        var paymentModeNames = {};

        // Create a mapping of payment type values to names
        if (paymentModes.length > 0) {
            $.each(paymentModes, function(index, mode) {
                paymentModeNames[mode.value] = mode.name;
            });
        }

        // Academic Year Summary Section
        if (res_data.academic_year_summary && res_data.academic_year_summary.length > 0) {
            html += '<div class="mb-4">';

            // Group academic year data by year and payment type
            var yearData = {};
            var yearTotals = {};

            $.each(res_data.academic_year_summary, function(index, item) {
                var year = item.year_name || 'N/A';
                var paymentType = paymentModeNames[item.payment_type] || item.payment_type || 'Unknown';
                var amount = parseFloat(item.total_amount) || 0;

                if (!yearData[year]) {
                    yearData[year] = {};
                    yearTotals[year] = 0;
                }

                yearData[year][paymentType] = amount;
                yearTotals[year] += amount;
            });

            html += '<table class="table table-bordered mb-4" id="datewiseSummaryData" >';
            html += '<thead>';
            html += '<tr><th>Academic Year</th><th>Amount</th></tr>';
            html += '</thead>';
            html += '<tbody>';

            var grand_total = 0;
            $.each(yearTotals, function(year, total) {
                html += '<tr>';
                html += '<td>' + year + '</td>';
                html += '<td>' + total.toFixed(2) + '</td>';
                html += '</tr>';
                grand_total += total;
            });

            html += '<tr class="bg-light font-weight-bold">';
            html += '<td><strong>Grand Total</strong></td>';
            html += '<td><strong>' + grand_total.toFixed(2) + '</strong></td>';
            html += '</tr>';
            html += '</tbody>';
            html += '</table>';
            html += '</div>';
        }

        // Date Wise Summary Section
        if (res_data.date_wise_summary && res_data.date_wise_summary.length > 0) {
            html += '<div class="date-wise-summary">';
            // Group data by date and payment mode (sum all fee types)
            var grouped_data = {};
            var payment_modes_used = new Set();

            $.each(res_data.date_wise_summary, function(index, item) {
                var date = item.payment_date;
                var payment_type = item.payment_mode;
                var payment_mode_name = paymentModeNames[payment_type] || payment_type || 'Unknown';
                var amount = parseFloat(item.total_amount) || 0;

                if (!grouped_data[date]) {
                    grouped_data[date] = {};
                }
                if (!grouped_data[date][payment_mode_name]) {
                    grouped_data[date][payment_mode_name] = 0;
                }

                grouped_data[date][payment_mode_name] += amount;
                payment_modes_used.add(payment_mode_name);
            });

            // Convert Set to Array and sort
            var payment_modes_array = Array.from(payment_modes_used).sort();

            html += '<div class="table-responsive">';
            html += '<table id="fee_summary_table" class="table table-bordered" style="width: 100%; table-layout: fixed;">';
            html += '<thead>';
            html += '<tr>';
            html += '<th>Date</th>';

            // Create header for each payment mode
            // Helper function to format payment mode string (capitalize each word)
            function formatPaymentMode(str) {
                return str.replace(/_/g, ' ').replace(/\w\S*/g, function(txt) {
                    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
                });
            }
            $.each(payment_modes_array, function(index, payment_mode) {
                if(payment_mode == '10'){
                    payment_mode = 'Online Payment';
                }
                if(payment_mode == '999'){
                    payment_mode = 'Excess Amount';
                }
                html += '<th>' + formatPaymentMode(payment_mode) + '</th>';
            });
            html += '<th>Grand Total</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';

            // Sort dates
            var sorted_dates = Object.keys(grouped_data).sort(function(a, b) {
              return new Date(a) - new Date(b);
            });

            $.each(sorted_dates, function(index, date) {
                html += '<tr>';
                html += '<td>' + date + '</td>';

                var row_total = 0;

                // For each payment mode, show the total amount
                $.each(payment_modes_array, function(pm_index, payment_mode) {
                    var amount = grouped_data[date][payment_mode] || 0;
                    html += '<td>' + (amount > 0 ? amount.toFixed(2) : '0') + '</td>';
                    row_total += amount;
                });

                // Grand total for the row
                html += '<td>' + row_total.toFixed(2) + '</td>';
                html += '</tr>';
            });

            html += '</tbody>';
            html += '</table>';
            html += '</div>'; // Close table-responsive
            html += '</div>'; // Close date-wise-summary
        } else {
            html += '<div class="text-center"><h5>No date-wise data found</h5></div>';
        }

        // Set the HTML content
        $('#daybookFeeSummary').html(html);

        // Initialize DataTable with export buttons for date-wise view
        if ($('#fee_summary_table').length) {
            var totalColumns = $('#fee_summary_table thead tr th').length;

            let table = $('#fee_summary_table').DataTable({
                dom: 'Bfrtip',
                paging: false,
                searching: true,
                ordering: false,
                info: false,
                scrollX: true,
                autoWidth: false,
                columnDefs: [
                    { width: "120px", targets: 0 }, // Date column
                    { width: "100px", targets: "_all" }, // All other columns
                    { className: "", targets: 0 }, // Center align date
                    { className: "", targets: "_all" } // Right align amounts
                ],
                buttons: [
                    {
                      extend: 'print',
                      text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
                      title: 'Fee Date Wise Summary Report',
                      messageTop: function() {
                        return '<div style="text-align: center; margin-bottom: 20px;">' +
                               '<h3>Fee Date Wise Summary Report</h3>' +
                               '<h5>From ' + $('#from_date').val() + ' To ' + $('#to_date').val() + '</h5>' +
                               '</div>';
                      },
                      footer: true,
                      exportOptions: {
                        columns: ':visible',
                      },
                      customize: function (win) {
                        // Include academic year summary table in print
                        var academicSummary = '';
                        if ($('#datewiseSummaryData').length > 0) {
                          academicSummary = '<div style="margin-bottom: 20px;"><h4>Academic Year Summary</h4>' +
                                          $('#datewiseSummaryData')[0].outerHTML + '</div>';
                        }

                        // Find the main table and insert academic summary before it
                        var mainTable = $(win.document.body).find('table').first();
                        if (mainTable.length > 0) {
                          mainTable.before(academicSummary);
                        } else {
                          $(win.document.body).prepend(academicSummary);
                        }

                        $(win.document.head).append(`
                          <style>
                            @page {
                              size: landscape;
                              margin: 12mm;
                            }
                            table {
                              font-size: 10px;
                              width: 100% !important;
                              table-layout: fixed;
                            }
                            th, td {
                              word-wrap: break-word;
                              padding: 4px !important;
                            }
                            .dt-print-view h3, .dt-print-view h5 {
                              margin: 10px 0;
                            }
                          </style>
                        `);
                      },
                    },
                    {
                        text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
                        action: function (e, dt, button, config) {
                            exportCompleteReport();
                        }
                    }
                ]
            });
        }
    }

    // Function to export complete report with both tables
    function exportCompleteReport() {
        var reportContent = '';

        // Add header
        reportContent += 'Fee Date Wise Summary Report\n';
        reportContent += 'From ' + $('#from_date').val() + ' To ' + $('#to_date').val() + '\n\n';

        // Add academic year summary if exists
        if ($('#datewiseSummaryData').length > 0) {
            reportContent += 'Academic Year,Amount\n';

            $('#datewiseSummaryData tbody tr').each(function() {
                var year = $(this).find('td:first').text().trim();
                var amount = $(this).find('td:last').text().trim();
                reportContent += year + ',' + amount + '\n';
            });
            reportContent += '\n';
        }

        // Add date wise summary
        if ($('#fee_summary_table').length > 0) {
            reportContent += 'Date Wise Summary:\n';

            // Add headers
            var headers = [];
            $('#fee_summary_table thead tr th').each(function() {
                headers.push($(this).text().trim());
            });
            reportContent += headers.join(',') + '\n';

            // Add data rows
            $('#fee_summary_table tbody tr').each(function() {
                var row = [];
                $(this).find('td').each(function() {
                    row.push($(this).text().trim());
                });
                reportContent += row.join(',') + '\n';
            });
        }

        // Create and download CSV file
        var blob = new Blob([reportContent], { type: 'text/csv;charset=utf-8;' });
        var link = document.createElement('a');
        var url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'Fee_Complete_Date_Wise_Summary_Report.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
</script>

<style>
   @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');
    table {
        font-family: 'Poppins', sans-serif !important;
    }
    .gap-left {
      margin-left: 20px !important;
    }
    .search-box {
      display: inline-block;
      position: relative;
      margin-right: 2px;
      vertical-align: middle;
    }

    .input-search {
      line-height: 1.5;
      padding: 5px 10px;
      display: inline;
      width: 177px;
      height: 27px;
      background-color: #f2f2f2 !important;
      border: 1px solid #ccc !important;
      border-radius: 4px !important;
      margin-right: 0 !important;
      font-size: 14px;
      color: #495057;
      outline: none;
      margin-bottom: 10px;
    }

    .input-search::placeholder {
      color: rgba(73, 80, 87, 0.5);
      font-size: 14px;
      font-weight: 300;
    }

    #fee_summary_table{
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
    }
    #fee_summary_table thead th{
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 13px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
    }
  
    #fee_summary_table tbody tr:nth-child(even) {
    background-color: #f9fafb;
    }

    #fee_summary_table tbody tr:hover {
    background-color: #f1f5f9;
    }
    #fee_summary_table tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
    }

.buttons-excel {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .buttons-print {
    padding: 2px !important;
  }

  .buttons-colvis {
    padding: 2px !important;
  }

  .dt-button {
    border: none !important;
    background: none !important;
  }

  .btn-info {
    border-radius: 8px !important;
  }

  .dt-button .btn {
    line-height: 20px;
  }

  .dt-buttons {
    text-align: right;
    float: right;
  }

  /* Date-wise summary table fixes */
  .date-wise-summary {
    overflow-x: auto;
  }

  .date-wise-summary .table {
    width: 100% !important;
    table-layout: fixed;
    margin-bottom: 0;
  }



  .academic-year-summary .table {
    max-width: 400px;
  }

  /* DataTable responsive fixes */
  .dataTables_wrapper {
    width: 100%;
    overflow-x: auto;
  }

  .dataTables_scrollHead,
  .dataTables_scrollBody {
    width: 100% !important;
  }

  .dataTables_scrollBody table {
    width: 100% !important;
  }

  /* Column visibility button styling */
  .dt-button-collection {
    max-height: 300px;
    overflow-y: auto;
  }

  .dt-button-collection .buttons-columnVisibility:before {
    content: ' ';
    margin-top: -6px;
    margin-left: 10px;
    border: 1px solid black;
    border-radius: 3px;
  }

  .dt-button-collection .buttons-columnVisibility:before,
  .dt-button-collection .buttons-columnVisibility.active span:before {
    display: block;
    position: absolute;
    top: 1.2em;
    left: 0;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
  }

  .dt-button-collection .buttons-columnVisibility span {
    margin-left: 20px;
  }

  .dt-button-collection .buttons-columnVisibility.active span:before {
    content: '\2714';
    /* Unicode checkmark character */
    margin-top: -13px;
    margin-left: 12px;
    text-align: center;
    text-shadow: 1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
  }

  div.dt-button-collection .dt-button {
    position: relative;
    left: 0;
    right: 0;
    width: 100%;
    display: block;
    float: none;
    background: none;
    margin: 0;
    padding: .5em 1em;
    border: none;
    text-align: left;
    cursor: pointer;
    color: inherit;
  }

</style>