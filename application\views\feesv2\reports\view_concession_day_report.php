<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fees Dashboard</a></li>
  <li>Concession Day Report</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <div class="d-flex justify-content-between" style="width:100%;">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a> 
              Concession Day Report
            </h3>
          </div>
        </div>
      </div>
    </div>

    <div class="card-body pt-1">
      
      <div class="col-md-12">
        <div class="row">
        
        <div class="col-md-2 form-group">
            <p for="fromdateId" class="control-label" style="margin-bottom: 4px; margin-left:3px;">Select Date range</p>
            <div id="reportrange" class="dtrange custom-select " style="width:  100%">                                            
                    <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
            </div>
        </div>

          <div class="col-md-2 form-group" id="multiBlueprintSelect">
            <p style="margin-bottom: 4px; margin-left:3px;">Select Fee Type</p>
            <select class="form-control select" id="blueprint_type" required="" name="fee_type">
              <option value=""><?php echo 'All Fee Type' ?></option>
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
          </div>


          <div class="col-md-2 form-group">
            <p style="margin-bottom: 4px; margin-left:3px;">Class</p>
            <?php 
                $array = array();
                foreach ($classes as $key => $class) {
                  $array[$class->classId] = $class->className; 
                }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='Select Classes' class='form-control classId select '");
            ?>
          </div>


           <div class="col-md-2 form-group">
            <p style="margin-bottom: 4px; margin-left:3px;">Select Class/Sections</p>
              <?php 
                $array = array();
                // $array[0] = 'All Section';
                foreach ($classSectionList as $key => $cl) {
                    $array[$cl->id] = $cl->class_name . $cl->section_name;
                }
                echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='Select Class/Section' class='form-control select'");
              ?>
            </div>

            <div class="col-md-2 form-group" style="margin-top: 22px;">
              <input type="button" value="Get Report" id="getReport" class="btn btn-primary">
            </div>
        </div>
          <div class="col-12 text-center loading-icon" style="display: none;">
            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
          </div>
          <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>

      </div>
      <div class="col-md-12 pt-2" style="overflow: hidden;" id="div_id">

        <div id="concession_day_data" class="fee_balance pt-3 table-responsive">
          
        </div>

      </div>
    </div>


  </div> 
</div>
<style type="text/css">
  #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  }
  .table>thead>tr>th{
    white-space: nowrap;
  }
</style>

<script>
  $(document).ready(function(){

    $("#reportrange").daterangepicker({
      ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
      },
      opens: 'right',
      buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment().subtract(6, 'days'),
        endDate: moment()            
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });

    $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
    $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));



    $('#getReport').on('click',function(){
      var fee_type = $('#blueprint_type').val();
      // if (fee_type =='') {
      //   return false;
      // }
      $('.loading-icon').show();
      $("#progress").show();
      $('#getReport').prop('disabled', true).val('Please wait...');
      $('.fee_balance').html('');
      $('.total_summary').html('');
      var classSectionId =  $('#classSectionId').val();
      var classId =  $('#classId').val();
      var from_date = $("#from_date").val();
      console.log(from_date);
	  var to_date = $("#to_date").val();

      $.ajax({
        url: '<?php echo site_url('feesv2/Reports/get_fee_concession_day_details'); ?>',
        data: {'fee_type':fee_type,'classSectionId':classSectionId,'classId':classId,'from_date':from_date,'to_date':to_date},
        type: "post",
        success: function (data) {
          $('.loading-icon').hide();
          $("#progress").hide();
          $('#getReport').prop('disabled', false).val('Get Report');
          var students = JSON.parse(data);
          // console.log(students);
          $("#concession_day_data").html(construct_table(students));

          const reportName=`Concession_day_report ${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;
          
          $('#concession_report_table').DataTable( {
            ordering:false,
            paging : false,
            scrollY :'40vh',
            "language": {
              "search": "",
              "searchPlaceholder": "Enter Search..."
            },
            dom: '<"dt-top-controls"fB>rtip',
            buttons: [
              {
              extend: 'excelHtml5',
              text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
              filename: reportName,
              },
              {
              // extend: 'print',
              text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
              action: function ( e, dt, node, config ) {
                customPrintConcession();
              }
              },
              {
              extend: 'pdfHtml5',
              text: '<button class="btn btn-info"><span class="fa fa-file-pdf-o" aria-hidden="true"></span> PDF</button>',
              filename: reportName,
              }
            ]
              });
        },
        error: function (err) {
          alert('Error');
          console.log(err);
        }
      });
    });
  });

  function customPrintConcession() {
    const printWindow = window.open('', '_blank');
    let printHeaderText = document.querySelector('.panel_title_new_style_staff')?.innerText || 'Concession Day Report';
    let summaryTable = document.querySelector('.total_summary')?.innerHTML || '';

    // Manually get thead and tbody and reconstruct the table
    let thead = $('#concession_report_table').closest('body').find('thead').first().prop('outerHTML');
    let tbody = $('#concession_report_table').closest('body').find('tbody').first().prop('outerHTML');
    let componentTable = '<h4>No data</h4>';
    if (thead && tbody) {
        componentTable = `<table style="width:100%;border-collapse:collapse;">${thead}${tbody}</table>`;
    }

    printWindow.document.write(`
        <html>
        <head>
            <title>Fee Concessions Report</title>
            <style>
            @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

                body {
                    font-family: 'Poppins', sans-serif;
                    padding: 20px;
                }
                h2, h3, h4, h5 {
                    margin: 5px 0;
                    text-align: center;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                    font-size: 11px;
                    background-color: #ffffff;
                    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 10px 14px;
                    text-align: left;
                }
                thead th {
                    background-color: #f1f5f9;
                    color: #111827;
                    font-weight: 500;
                }
                #print_visible {
                    display: block !important;
                }
                @media print {
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <h2>${printHeaderText}</h2>
            ${summaryTable ? '<h3>Fee Summary</h3>' + summaryTable : ''}
            ${componentTable}
            <script>
            window.onload = function() {
                window.print();
            };
            window.onafterprint = function() {
                window.close();
            };
            <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

  function construct_table(data){
    // console.log(data);
    var html ='';

  if (data.length == 0) {   
    html += '<h4>No data </h4>';
  }
  else{
      html+=`<table id="concession_report_table" class="table table-bordered">
            <thead>
              <tr style="white-space: nowrap">
                <th>#</th>
                <th>Fee Type</th>
                <th>Student Name</th>
                <th>Class/Section</th>
                <th>Created On</th>
                <th>Remarks</th>
                <th>Amount</th>
                <th>Created By</th>
              </tr>
            </thead>`;
      html += `<tbody>`;
      for (let i = 0; i < data.length; i++) {
        
      }
      for(var i=0;i<data.length;i++){
      // console.log(data_val);
      html+=`
              <tr>
                <td>${i+1}</td>
                <td>${data[i].fee_type}</td>
                <td>${data[i].student_name}</td>
                <td>${data[i].class_section}</td>
                <td>${data[i].create_date}</td>`;
                if(data[i].remarks){
                  html+=`<td>${data[i].remarks}</td>`;
                }else{
                  html+=`<td style="text-align: center;">-</td>`;
                }
                html+=`<td>${data[i].concession_amount}</td>
                <td>${data[i].created_name}</td>
                </tr>`;
      }
      html+=`</tbody>
        </table>`; 
    }
    return html;
  }


</script>

<style type="text/css">
   @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

    table {
        font-family: 'Poppins', sans-serif !important;
    }

    ::-webkit-scrollbar {
    width: 10px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
    background: #f1f1f1;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
    background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
    background: #555;
    }
  .widthadjust{
  width:600px;
  margin:auto;
  }
  .dataTables_scrollBody{
    margin-top: -13px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .form-horizontal .control-label{
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;

  }
  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
    
	}

	.dataTables_filter input {
		    line-height: 1.5;
        padding: 5px 10px;
        display: inline;
        width: 177px;
        height: 27px;
        background-color: #f2f2f2 !important;
        border: 1px solid #ccc !important;
        border-radius: 4px !important;
        margin-right: 0 !important;
        font-size: 14px;
        color: #495057;
        outline: none;   
	}
	.dataTables_filter ::placeholder {
        color: rgba(73, 80, 87, 0.5);
        font-size: 14px;
        font-weight: 300;
    }
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
    border-bottom:none;
	}

  .dt-button{
    border: none !important;
    background: none  !important;
  }
  .btn-info{
      border-radius: 8px !important; 
  }
  .dt-button .btn{
    line-height:20px;
  }
  .dt-buttons{
    text-align: right;
    float:right;
  }
  .dt-top-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.dt-top-controls .dataTables_filter {
    margin: 0;
    order: 1;
    margin-bottom: -7px !important;
}

.dt-top-controls .dt-buttons {
    order: 2;
}

.dataTables_filter {
    float: none !important;
    position: static !important;
}

.dt-buttons {
    float: none !important;
    text-align: right !important;
}

#concession_report_table {
        width: 100%;
        border-collapse: collapse;
        background-color: #ffffff;
        border-radius: 1.5rem;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
        opacity: 1 !important;
        transition: none !important;
    }

    #concession_report_table thead th{
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        color: #111827;
        font-size: 11px;
        font-weight: 500;
        z-index: 10;
        text-align: left;
        padding: 12px 16px;
    }

    #concession_report_table th,
    #concession_report_table td {
        padding: 10px 14px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 11px;
        font-weight: 400;
    }

    #concession_report_table tbody tr:nth-child(even) {
        background-color: #f9fafb;
    }

    #concession_report_table tbody tr:hover{
        background-color: #f1f5f9;
    }

    #concession_report_table tfoot tr {
        background-color: #f3f4f6;
        font-weight: 500;
    }

    .concession_report_table {
        margin: 12px 0;
        overflow-x: auto;
        max-height: 500px;
        scrollbar-width: thick; /* For Firefox */
        scrollbar-color: #cbd5e1 #f1f5f9; /* For Firefox */
    }
    .concession_report_table::-webkit-scrollbar {
        height: 16px;
        width: 16px;
    }
    .concession_report_table::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 8px;
    }
    .concession_report_table::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 8px;
    }
    #concession_report_table thead th {
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        z-index: 2;
    }
    #table-toolbar {
        position: static !important;
        background: none !important;
        z-index: 1;
    }
    /* For Firefox */
    .concession_report_table {
        scrollbar-width: thick;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 16%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}	
	}
</style>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

