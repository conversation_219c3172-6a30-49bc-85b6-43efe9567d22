<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard') ?>">Fee Dashboard</a></li>
  <li class="active">Re-generate PDF Receipt</li>
</ul>
<div class="col-md-12">
<div class="panel panel-default new-panel-style_3">
    <div class="panel-heading new-panel-heading">
        <h3 class="panel-title"><strong>Re-generate PDF Receipt</strong></h3>
        <ul class="panel-controls">
            <div class="col-md-8">
                <select class="form-control" id="fee_type" name="fee_type" style="width: 120px;" >
                    <option value="0">All</option>
                <?php foreach ($fee_types as $key => $val) { ?>
                    <option <?php if($selectedBP === $val->id) echo 'selected' ?> value="<?= $val->id ?>"><?php echo $val->name ?></option>
                <?php } ?>
                </select>
            </div>
        </ul>
    </div>
    <div class="panel-body">
        <div class="form-group col-md-4 text-center" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
            <h5>Search By Class</h5>
            <div class="col-md-8 col-md-offset-2">
                <?php 
                    $array = array();
                    $array[0] = 'Select Class';
                    foreach ($classList as $key => $cl) {
                        $array[$cl->classId] = $cl->className;
                    }
                    echo form_dropdown("classId", $array, set_value("classId"), "id='classId' class='form-control'" );
                ?>
            </div>
        </div>
         
        <!-- <div class="form-group col-md-4 text-center" style="border-radius:5px; box-shadow:3px 3px 3px #ccc;padding-bottom:5px;">
            <div class="form-horizontal">
                <h5>Search By Student Name</h5>
                <div class="col-md-8 col-md-offset-2">
                    <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1">
                </div>
            </div>
        </div> -->

        <div class="col-md-4">
            <div class="form-group text-center hidden-xs" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                <div class="form-horizontal">
                    <h5>Search By Student Name</h5>
                    <div class="row">
                        <div class="col-md-8 col-md-offset-1">
                            <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1">
                            
                        </div>
                        <div class="col-md-2">
                            <input type="button" value="Get" id="getByStdName" class="input-md btn btn-primary">
                        </div>
                        <div class="col-md-12">
                            <span class="help-block">Enter few letters of name and click Enter/Get</span>
                        </div>
                    </div>
                </div>
            </div>                
        </div>

        <div class="form-group col-md-4 text-center" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
            <div class="form-horizontal">
                <h5>Search By Receipt #</h5>
                <div class="col-md-6 col-md-offset-2">
                    <input id="receipt_number" autocomplete="off" placeholder="Search by receipt No" class="form-control input-md" name="receipt_number">
                </div>
                <div class="col-md-2">
                    <input type="button" value="Get" id="getreceipt_number" class="input-md btn  btn-primary">
                </div>
            </div>
        </div>

    </div>
    <?php if ($this->authorization->isSuperAdmin()) { ?>
        <div class="panel-body">
            <div class="form-group col-md-4">
                <div class="form-horizontal">
                      <label for="from_date" class="col-sm-5 control-label">From Date:</label>
                    <div class="col-sm-7">
                        <input type="text" class="form-control" id="from_date" name="from_date" value="<?php echo date('d-m-Y'); ?>" >
                    </div>
                </div>
            </div>
            <div class="form-group col-md-4">
                <div class="form-horizontal">
                    <label for="to_date" class="col-sm-5 control-label">To Date:</label>
                    <div class="col-sm-7" >
                        <input type="text" class="form-control" id="to_date" name="to_date" value="<?php echo date('d-m-Y'); ?>" >
                    </div>
                </div>
            </div>
            <div class="form-group col-md-2">
                  <center>
                    <div class='form-group'>
                      <input type="button" name="search" id="search_date" class="btn btn-primary" value="Search">
                    </div>
                  </center>
            </div>
        </div>
    <?php } ?>
   

</div>

<div class="panel panel-default new-panel-style_3">
    <div class="panel-heading new-panel-heading">
        <h3 class="panel-title"><strong>Student List </strong></h3>
        <ul class="panel-controls">
            <button type="button" onclick="generate_pdf_checked()" id="generate_pdfId"  class="btn btn-primary">Genearte PDF</button>
        </ul>
    </div>

    <form enctype="multipart/form-data" id="publish-form-assigned" action="<?php echo site_url('feesv2/fees_student/publish_fee_assigned') ?>" class="form-horizontal" data-parsley-validate method="post">
        <input type="hidden" name="class_id"  id="select_class_id">
        <input type="hidden" id="blueprint_id" name="blueprint_id" value="<?php echo $selectedBP ?>">
        <div class="panel-body stdudentData hidden-xs leaveData">
            <h5>Select filter to Student list</h5>
        </div> <!--Panel body-->
    </form>
</div>
</div>
<style>
#tags{
    position:relative
    padding: 10px;
}
.autocomplete-items {
  position: absolute;
  overflow-y:auto;
  border-bottom: none;
  border-top: none;
  height:300px;
  margin:0px 15px;
  z-index: 99;
  /*position the autocomplete items to be the same width as the container:*/
  top: 100%;
  left: 0;
  right: 0;
}
.autocomplete-items div {
  padding: 10px;
  cursor: pointer;
  background-color: #fff; 
  border-bottom: 1px solid #d4d4d4; 
}
.autocomplete-items div:hover {
  /*when hovering an item:*/
  background-color: #e9e9e9; 
}
.autocomplete-active {
  /*when navigating through the items using the arrow keys:*/
  background-color: DodgerBlue !important; 
  color: #ffffff; 
}
</style>

<script type="text/javascript">
    $(function () {
    class_change_data(); //this calls it on load
        $("#classId").change(class_change_data);
    });

function class_change_data(classid){
  var classId = $("#classId").val();
  var selectblueprintid = $("#fee_type").val();
    if(classId) {
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
            type: 'post',
            data: {'classId':classId,'mode':'class_id','selectblueprintid':selectblueprintid},
            success: function(data) {
                var std = JSON.parse(data);
                $(".stdudentData").html(prepare_student_table(std));
            }
        });
    }
   }

$("#search_date").click(function(){
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var selectblueprintid = $("#fee_type").val();
    if(receipt_number) {
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
            type: 'post',
            data: {'from_date':from_date,'to_date':to_date,'mode':'date','selectblueprintid':selectblueprintid},
            success: function(data) {
                var std = JSON.parse(data);
                $(".stdudentData").html(prepare_student_table(std));
            }
        });
    }
});

 

</script>
<script type="text/javascript">
  $(document).ready(function(){
    $('#from_date,#to_date').datepicker({
      format: 'dd-mm-yyyy',
      todayHighlight: true,
      "autoclose": true
    });   
  });
</script>
<script>
    // var names = [];
    $(document).ready(function(){
    /*var stdNames = JSON.parse('<?php //echo json_encode($studentNames); ?>');
    for(var i=0; i<stdNames.length; i++){
        names.push(stdNames[i].stdName);
    }*/
    var classId = $("#classId").val();
    var blueprint_id = $('#blueprint').val();
    class_change_data(classId);

    $("#getreceipt_number").click(function(){
        var receipt_number = $("#receipt_number").val();
        var blueprint_id = $('#blueprint').val();
        var selectblueprintid = $("#fee_type").val();

        if(receipt_number) {
            $.ajax({
                url: '<?php echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
                type: 'post',
                data: {'receipt_number':receipt_number,'mode':'receipt_number','blueprint_id':blueprint_id,'selectblueprintid':selectblueprintid},
                success: function(data) {
                    var std = JSON.parse(data);
                    $(".stdudentData").html(prepare_student_table(std));
                }
            });
        }
    });

});

function prepare_student_table(std) {
    console.log(std);
    var html = '';
    
    if(!std)
        html += "<h4>Select filter to get student data</h4>";
    else {
        html += '<table id="customers2" class="table datatable"><thead><tr><th>#</th><th>Receipt #</th><th>Student name</th><th>Admission No</th><th>Class</th><th width="15%">Actions</th><th style="text-align: center;" width="15%"><input type="checkbox" name="selectAll" onclick="check_all(this)" id="selectAll" class="check"></th></tr></thead><tbody>';
        for (i=0, j=0; i < std.length; i++) {
            html += "<tr><td>" + (i+1) + "</td>";
            html += "<td>" + std[i].receipt_number + "</td>";
            html += "<td>" + std[i].stdName + "</td>";
            html += "<td>" + std[i].admission_no + "</td>";
            html += "<td>" + std[i].clsName + "</td>";
            html += "<td>" + std[i].pdfStatus + "</td>";

            html += "<td>";
            html += "<input type='checkbox' name='transcation_ids[]' value='"+std[i].transId+"' class='pdf_generateCheck'>";
            html += "</td>";
            html += '</tr>';
        }
        html += '</tbody></table>';
    }
    return html;
}
</script>


<script type="text/javascript">
    function generate_pdf_checked() {
        var checked_transids = [];
        $('.pdf_generateCheck:checked').each(function(){
          checked_transids .push($(this).val());
        });
        $('#generate_pdfId').html('Please wait..').prop('disabled',true);
        $.ajax({
          url: '<?php echo site_url('feesv2/fees_collection/generate_pdf_fee_receipt'); ?>',
          data: {'checked_transids': checked_transids},
          type: "post",
          success: function (data) {
            $('#generate_pdfId').html('Genearte PDF').prop('disabled',false);
            if(data == 1){
                 new PNotify({
                  title: 'Success',
                  text:  'PDF Generate successfully',
                  type: 'success',
                });
             }else{
                 new PNotify({
                  title: 'Error',
                  text:  'Something went wrong',
                  type: 'error',
                });
             }
           
            console.log(data);
          },
          error: function (err) {
            console.log(err);
          }
        });
    }
</script>

<style type="text/css">
    input[type="checkbox"]{
      width: 20px; 
      height: 20px;
    }
</style>
<script type="text/javascript">

   function check_all(check){
        if($(check).is(':checked')) {
            $(".pdf_generateCheck").prop('checked', true);
        }
        else {
            $(".pdf_generateCheck").prop('checked', false);
        }
    }

</script>   
<script>
// autocomplete(document.getElementById("stdName1"), names);
// var stdName = '';
// var blueprint_id = $('#blueprint').val();

// function autocomplete(inp, arr) {
//     /*the autocomplete function takes two arguments,
//     the text field element and an array of possible autocompleted values:*/
//     var currentFocus;
//     /*execute a function when someone writes in the text field:*/
//     inp.addEventListener("input", function(e) {
//         var a, b, i, val = this.value;
//         /*close any already open lists of autocompleted values*/
//         closeAllLists();
//         if (!val) { return false;}
//         currentFocus = -1;
//         /*create a DIV element that will contain the items (values):*/
//         a = document.createElement("DIV");
//         a.setAttribute("id", this.id + "autocomplete-list");
//         a.setAttribute("class", "autocomplete-items");
//         /*append the DIV element as a child of the autocomplete container:*/
//         this.parentNode.appendChild(a);
//         /*for each item in the array...*/
//         for (i = 0; i < arr.length; i++) {
//             /*check if the item starts with the same letters as the text field value:*/
//             if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
//             /*create a DIV element for each matching element:*/
//             b = document.createElement("DIV");
//             /*make the matching letters bold:*/
//             b.innerHTML = "<strong>" + arr[i].substr(0, val.length) + "</strong>";
//             b.innerHTML += arr[i].substr(val.length);
//             /*insert a input field that will hold the current array item's value:*/
//             b.innerHTML += "<input type='hidden' value='" + arr[i] + "'>";
//             /*execute a function when someone clicks on the item value (DIV element):*/
//                 b.addEventListener("click", function(e) {
//                 /*insert the value for the autocomplete text field:*/
//                 inp.value = this.getElementsByTagName("input")[0].value;
//                 stdName = this.getElementsByTagName("input")[0].value;
//                 $.ajax({
//                     url: '<?php //echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
//                     type: 'post',
//                     data: {'name':stdName,'mode':'name','blueprint_id':blueprint_id},
//                     success: function(data) {
//                         var std = JSON.parse(data);
//                         $(".stdudentData").html(prepare_student_table(std));
//                     }
//                 });
//                 /*close the list of autocompleted values,
//                 (or any other open lists of autocompleted values:*/
//                 closeAllLists();
//             });
//             a.appendChild(b);
//             }
//         }
//     });
//     /*execute a function presses a key on the keyboard:*/
//     inp.addEventListener("keydown", function(e) {
//         var x = document.getElementById(this.id + "autocomplete-list");
//         if (x) x = x.getElementsByTagName("div");
//         if (e.keyCode == 40) {
//             /*If the arrow DOWN key is pressed,
//             increase the currentFocus variable:*/
//             currentFocus++;
//             /*and and make the current item more visible:*/
//             addActive(x);
//         } else if (e.keyCode == 38) { //up
//             /*If the arrow UP key is pressed,
//             decrease the currentFocus variable:*/
//             currentFocus--;
//             /*and and make the current item more visible:*/
//             addActive(x);
//         } else if (e.keyCode == 13) {
//             /*If the ENTER key is pressed, prevent the form from being submitted,*/
//             e.preventDefault();
//             if (currentFocus > -1) {
//             /*and simulate a click on the "active" item:*/
//             if (x) x[currentFocus].click();
//             }
//         }
//     });
//     function addActive(x) {
//         /*a function to classify an item as "active":*/
//         if (!x) return false;
//         /*start by removing the "active" class on all items:*/
//         removeActive(x);
//         if (currentFocus >= x.length) currentFocus = 0;
//         if (currentFocus < 0) currentFocus = (x.length - 1);
//         /*add class "autocomplete-active":*/
//         x[currentFocus].classList.add("autocomplete-active");
//     }
//     function removeActive(x) {
//         /*a function to remove the "active" class from all autocomplete items:*/
//         for (var i = 0; i < x.length; i++) {
//         x[i].classList.remove("autocomplete-active");
//         }
//     }
//     function closeAllLists(elmnt) {
//         /*close all autocomplete lists in the document,
//         except the one passed as an argument:*/
//         var x = document.getElementsByClassName("autocomplete-items");
//         for (var i = 0; i < x.length; i++) {
//         if (elmnt != x[i] && elmnt != inp) {
//         x[i].parentNode.removeChild(x[i]);
//         }
//     }
//     }
//     /*execute a function when someone clicks in the document:*/
//     document.addEventListener("click", function (e) {
//         closeAllLists(e.target);
//     });
// }

    $("#stdName1").keydown(function(e) {
        if(e.keyCode == 13) {
            getByStdName();
        }
    });

    $("#getByStdName").click(function (){
        getByStdName();
    });

    function getByStdName() {
        var name = $("#stdName1").val();
        var blueprint_id = $('#blueprint').val();
        var selectblueprintid = $("#fee_type").val();
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/student_fee_pdf_receipt'); ?>',
            type: 'post',
            data: {'name':name,'mode':'name','blueprint_id':blueprint_id,'selectblueprintid':selectblueprintid},
            success: function(data) {
                var std = JSON.parse(data);
                $(".stdudentData").html(prepare_student_table(std));
            }
        });
    }
</script>



