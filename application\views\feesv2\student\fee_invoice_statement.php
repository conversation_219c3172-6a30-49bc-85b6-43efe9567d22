<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Fees Invoice/Statement</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>"><span class="fa fa-arrow-left"></span></a>Details of <strong style="color:">Fees Invoice/Statement</strong>
                    </h3>
                </div>
            </div>
        </div>

        <div class="card-body">

            <div class="col-md-2">
                <label class="control-label mr-3">Year</label>
                <select class="form-control" name="acad_year" id="fee_acad_year">
                    <option value=''>Select Year</option>
                    <?php foreach ($fee_acad_year as $yearid => $year) { ?>
                        <option value="<?php echo $yearid ?>"><?php echo $year ?></option>
                    <?php } ?>
                </select>
            </div>

            <div class="col-md-3">
                <label class="col-md-12" for="gradeView" style="font-size: 14px;">Class</label>
                <div class="col-md-12">
                    <?php 
                    $array = array();
                    $array[0] = 'Select Class';
                    foreach ($classList as $key => $cl) {
                        $array[$cl->classId] = $cl->className;
                    }
                    echo form_dropdown("classSectionId", $array, '', "id='classSectionId' class='form-control'");
                    ?>
                </div>
            </div>

            <div class="col-md-3">
                <label class="col-md-12" for="gradeView">Student Name</label>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-10">
                            <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1"> 
                        </div>
                        <div class="col-md-2">
                            <input type="button" value="Get" id="getByStdName" class="input-md btn btn-primary">
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-md-3">
                <label class="col-md-12" for="gradeView">Admission No.</label>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-10">
                            <input id="admission_no" autocomplete="off" placeholder="Search by Admission No" class="form-control input-md" name="admission_no">
                        </div>
                        <div class="col-md-2">
                            <input type="button" value="Get" id="getByAdmissionNo" class="input-md btn  btn-primary">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="card-body">
            <div class="form-group col-md-4">
                <label class="control-label mr-3">Report </label>
                <label class="radio-inline" for="type-2">
                    <input type="radio" name="report_type" id="type-2" value="2" checked="">Summary
                </label>
                <label class="radio-inline" for="type-1">
                    <input type="radio" name="report_type" id="type-1" value="1">Detailed
                </label>
            </div>

        </div> -->
        

        <div class="card-body">
            <div class="col-12 text-center loading-icon" style="display: none;">
                <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
            </div>
            <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
            <div class="text-right mb-3">

            </div>
            <div>
                
            <div>
            <div id="expenseProgress" style="display:none;text-align:center;margin-bottom:10px;">
                <span id="expenseProgressText">Please wait...</span>
                <div class="progress" style="height: 8px;">
                    <div id="expenseProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="stdudentData table-responsive">
                <h5>Select filter to get Student list</h5>
            </div>

           
        </div>

    </div>
</div>

<script>

var total_students = 0;
  var completed = 0;
    $('#classSectionId').on('change',function(){
        var seletced = $(this).val();
        if (seletced != null) {
            class_section_wise_student_data();
        }else{
            $('.stdudentData').html('');
        }
    });
    $('input[name="report_type"]').change(function() {
        class_section_wise_student_data();
    });
    
    $("#stdName1").keydown(function(e) {
        if(e.keyCode == 13) {
            getByStdName();
        }
    });

    $("#getByStdName").click(function (){
        getByStdName();
    });
    $("#getByAdmissionNo").click(function(){
        getByAdmission();
    });

    $('#fee_acad_year').on('change',function(){
        class_section_wise_student_data();
        getByStdName();
        getByAdmission();
    });
    function getByAdmission() {
        var admission_no = $("#admission_no").val();
        if (admission_no == null || admission_no =='') {
            $('.stdudentData').html('');
            return false;
        }
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_student_v2/class_section_wise_student_data'); ?>',
            type: 'post',
            data: {'admission_no':admission_no},
            success: function(data) {
                var data = JSON.parse(data);
                if (data.length > 0) {
                    var students = data;
                    studentIds = students;
                    total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
                    var progress = document.getElementById('progress-ind');
                    progress.style.width = (completed/total_students)*100+'%';
                    $("#progress").show();
                    report_index(0);
                }
               
            }
        });
    }

    function getByStdName() {
        var stdName = $("#stdName1").val();
        if (stdName == null || stdName =='') {
            $('.stdudentData').html('');
            return false;
        }
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_student_v2/class_section_wise_student_data'); ?>',
            type: 'post',
            data: {'stdName':stdName},
            success: function(data) {
                var data = JSON.parse(data);
                var students = data;
                studentIds = students;
                total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
                var progress = document.getElementById('progress-ind');
                progress.style.width = (completed/total_students)*100+'%';
                $("#progress").show();
                report_index(0);
            }
        });
    }

    function class_section_wise_student_data(){
        var classSectionId = $('#classSectionId').val();
        if (classSectionId == null || classSectionId =='') {
            $('.stdudentData').html('');
            return false;
        }
        total_students = 0;
        completed = 0;
        if(classSectionId) {
            $.ajax({
            url: '<?php echo site_url('feesv2/fees_student_v2/class_section_wise_student_data'); ?>',
            type: 'post',
            data: {'classSectionId':classSectionId},
            success: function(data) {
                $('.loading-icon').hide();
                var data = JSON.parse(data);
                if(data.length > 0){
                    var students = data;
                    studentIds = students;
                    total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
                    var progress = document.getElementById('progress-ind');
                    progress.style.width = (completed/total_students)*100+'%';
                    $("#progress").show();
                    report_index(0);
                }
               
            }
            });
        }
    }

    function report_index(index) {
        if(index < studentIds.length) {
            get_fee_report(index);
        }else{
            $("#progress").hide();
        $('#fee_student_statment').DataTable( {
            ordering:false,
            paging : false,
            "language": {
              "search": "",
              "searchPlaceholder": "Enter Search..."
            },
            "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                "pageLength": 10,
            dom: 'lBfrtip',
            buttons: [
              {
              extend: 'excelHtml5',
              text: 'Excel',
              filename: 'Statement',
              className: 'btn btn-info'
              },
              {
              extend: 'print',
              text: 'Print',
              filename: 'Statement',
              className: 'btn btn-info'
              }
            ]
        });

        }
    }

    function get_fee_report(index) {
        var student_ids = studentIds[index];
        var blueprint = $('#blueprint').val();
        var fee_acad_year = $('#fee_acad_year').val();
        if(fee_acad_year == ''){
            return false;
        }
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_student/get_fee_invoice_statement_report'); ?>',
            type: 'post',
            data: {'student_ids':student_ids,'fee_acad_year':fee_acad_year},
            success: function(data) {
                var stdData = JSON.parse(data);
                console.log(stdData);
                var yearDisplay = 0;
                if (index == 0) {
                    // for (let k = 0; k < stdData.length; k++) {
                    //     yearDisplay = stdData[k].fees;
                    // }
                    // constructFeeHeader(yearDisplay);
                    construct_fee_header_statement();
                }
                completed += Object.keys(stdData).length;
                var progress = document.getElementById('progress-ind');
                progress.style.width = (completed/total_students)*100+'%';
                // prepare_invoice_statement_student_table(stdData, index, yearDisplay);
                prepare_fees_statement_student_table(stdData, index);
                // var report_type = $('input[name="report_type"]:checked').val();
                $('.summary').hide();
                $('.detailed').hide();

                // if(report_type == 1){
                //     $('.summary').hide();
                //     $('.detailed').show();
                // }else{
                //     $('.summary').show();
                //     $('.detailed').hide();
                // }
            }
        });
    }

    function prepare_fees_statement_student_table(stdData, index){
        var srNo = index * 100;
        var html = '';
        html += '<tbody>';
        for (var i in stdData){
        var obBalance = stdData[i].openingBalance;
        var total_payable_fee = parseFloat(stdData[i].current_total_payable_fee + obBalance - stdData[i].fullFeeDiscountFee - stdData[i].current_total_concession);
        let totalReceivedFee = parseFloat(stdData[i].totalReceivedFee) || 0;
        let openingBalanceReceived = parseFloat(stdData[i].openingBalanceReceived) || 0;
        let total_ob_excess_recived = parseFloat(stdData[i].total_ob_excess_recived) || 0;
        let total_ob_excess_return = parseFloat(stdData[i].total_ob_excess_return) || 0;
        var total_received_fee = totalReceivedFee + openingBalanceReceived - total_ob_excess_recived - total_ob_excess_return;

        let current_total_expenses =  parseFloat(stdData[i].current_total_expenses) || 0;
        let current_total_expenses_concession =  parseFloat(stdData[i].current_total_expenses_concession) || 0;
        

        var currentTotalExpenses = current_total_expenses - current_total_expenses_concession;
       
        html += '<tr>';
        html += '<td><input type="checkbox"  value='+i+' class="checkbox_invoice_state"><a id="downloadStatement'+i+'" onclick="download_invoice('+i+',\''+'Statement'+'\')" style="float: right;display: none;" class="btn btn-info btn-sm show_statement"><span class="fa fa-cloud-download"></span></a></td>';
        html += '<td>'+stdData[i].stdName+'</td>';
        html += '<td>'+stdData[i].className+'</td>';
        html += '<td>'+stdData[i].admission_no+'</td>';
        html += '<td>'+stdData[i].current_total_payable_fee+'</td>';
        html += '<td>'+stdData[i].current_total_concession+'</td>';
        html += '<td>'+obBalance.toFixed(2) +'</td>';
        html += '<td>'+(total_payable_fee).toFixed(2)+'</td>';

        html += '<td>'+(total_received_fee).toFixed(2)+'</td>';
        
        html += '<td>'+(stdData[i].current_total_other_expenses - stdData[i].current_total_other_concession).toFixed(2)+'</td>';

        html += '<td>'+(currentTotalExpenses).toFixed(2)+'</td>';
        html += '<td>'+stdData[i].current_acad_year_excess_amount+'</td>';

        var CurrentTotalExcessAmount = (stdData[i].total_excess_amount + total_ob_excess_recived) - (stdData[i].total_ob_excess_bal - total_ob_excess_return);
        
        var BalanceCurrentYear = (total_payable_fee + stdData[i].current_total_other_expenses + current_total_expenses) - (total_received_fee + stdData[i].current_total_other_concession + current_total_expenses_concession + CurrentTotalExcessAmount) 
            
        html += '<td>'+BalanceCurrentYear.toFixed(2)+'</td>';
        html += '<td>'+(stdData[i].next_year_total_payable_fee - stdData[i].next_year_concession)+'</td>';
        html += '<td>'+stdData[i].totalReceivedFee_nextYear+'</td>';
        html += '<td>'+(BalanceCurrentYear + stdData[i].next_year_total_payable_fee - stdData[i].totalReceivedFee_nextYear -  stdData[i].next_year_concession).toFixed(2)+'</td>';
        html += '</tr>';
        
    }
    html += '</tbody>';
    $('#fee_student_statment').append(html);
    index++;
    report_index(index);
    }

$(document).on('change', '.checkbox_invoice_state, #selectAll', function() {
    let allChecked = $('.checkbox_invoice_state').length > 0 &&
        $('.checkbox_invoice_state:checked').length === $('.checkbox_invoice_state').length;
    $('#generateStatementBtn').prop('disabled', !allChecked);
});

function importBooks() {
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/get_inventory_expense_offers_data'); ?>',
      type: 'post',
      data: {'std_id':std_id},
      success: function(data) {
        var resData = JSON.parse(data);
        console.log(resData);
        if(resData){
          $('#other_expense_model').modal('show');
          $('#inventory_expense_data').html(inventory_expense_construct_data(resData, std_id));
        }else{
          $('#inventory_expense_data').html('<h3 class="no-data-display">Result not found</h3>');
        }
      }
    });
}

function generate_expense_data() {
    var checkedStateInvoicestds = [];
    $('.checkbox_invoice_state:checked').each(function(){
        checkedStateInvoicestds.push($(this).val());
    });
    if (checkedStateInvoicestds.length > 0) {
        $('#importExpenseBtn').html('Generating..');
        showExpenseProgress(0, checkedStateInvoicestds.length);

        // Sequentially process each student using Promises
        let promise = Promise.resolve();
        checkedStateInvoicestds.forEach(function(student_id, idx) {
            promise = promise.then(function() {
                return generate_expense_data_bulk(student_id).then(function() {
                    showExpenseProgress(idx + 1, checkedStateInvoicestds.length);
                });
            });
        });

        promise.then(function() {
            $('#importExpenseBtn').html('Import Expense');
            hideExpenseProgress();
        });
    }
}

function generate_expense_data_bulk(student_id) {
    return new Promise(function(resolve, reject) {
        $.ajax({
            url:'<?php echo site_url('feesv2/fees_student/generate_expense_data_bulk') ?>',
            type:'post',
            data:{'student_id':student_id},
            success:function(data){
                var resData = JSON.parse(data);
                if (resData) {
                    // If resData is an object, process each expense
                    let expensePromises = [];
                    for(var name in resData){
                        let expense_name = resData[name].name;
                        let std_id = student_id;
                        let allocated_amount = resData[name].fee_total_amount;
                        let fee_total_amount = resData[name].amount == null ? 0 : resData[name].amount;
                        expensePromises.push(
                            generate_fees_expense_exits_data(expense_name, std_id, allocated_amount, fee_total_amount)
                        );
                    }
                    // Wait for all expenses for this student to finish
                    Promise.all(expensePromises).then(resolve);
                } else {
                    new PNotify({
                        title: 'Error',
                        text:  'Statement template not added',
                        type: 'error',
                    });
                    resolve();
                }
            },
            error: function() {
                resolve();
            }
        });
    });
}


function generate_fees_expense_exits_data(expense_name, std_id, allocated_amount, fee_total_amount) {
    return new Promise(function(resolve, reject) {
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/generate_expense_fees_component_wise_data'); ?>',
            type: 'post',
            data: {'std_id':std_id, 'expense_name':expense_name, 'allocated_amount':allocated_amount, 'fee_total_amount':fee_total_amount},
            success: function(data) {
                var res = JSON.parse(data);
                // Optionally, disable the checkbox for this student
                $('.checkbox_invoice_state[value="'+std_id+'"]').prop('disabled', true);
                if(res == -1){
                    console.log('already generated');
                }else if(res == 0){
                    console.log('empty data');
                }else{
                    console.log('Generated');
                }
                resolve();
            },
            error: function() {
                resolve();
            }
        });
    });
}

// Progress bar helpers
function showExpenseProgress(current, total) {
    $('#expenseProgress').show();
    let percent = total === 0 ? 0 : Math.round((current / total) * 100);
    $('#expenseProgressBar').css('width', percent + '%');
    $('#expenseProgressText').text('Please wait... (' + current + ' of ' + total + ')');
    if (current === total) {
        $('#expenseProgressText').text('Completed!');
        setTimeout(hideExpenseProgress, 1200);
    }
}
function hideExpenseProgress() {
    $('#expenseProgress').hide();
    $('#expenseProgressBar').css('width', '0%');
    $('#expenseProgressText').text('Please wait...');
}

function generateStatementAll(){
    var checkedStateInvoicestds = [];
    $('.checkbox_invoice_state:checked').each(function(){
        checkedStateInvoicestds.push($(this).val());
    });
    if(checkedStateInvoicestds.length > 0){
        $('#generateStatementBtn').html('Generating..');
        for (let istd = 0; istd < checkedStateInvoicestds.length; istd++) {
            generate_statement_bulk(checkedStateInvoicestds[istd]);
        }
    }
}
    function construct_fee_header_statement(){
        var currenct_year = '<?php echo $this->acad_year->getAcadYear() ?>';
        var nextAcadeYear = '<?php echo $this->acad_year->getPromotionAcadYear() ?>';        
        var html = '';
        html += '<div class="text-left mb-2 d-flex flex-wrap gap-2">';
        html += '<button id="generateStatementBtn" onclick="generateStatementAll()" class="btn btn-primary mr-2" disabled>Generate Statement</button>';
        html += '<button id="importExpenseBtn" class="btn btn-secondary mr-2" onclick="generate_expense_data()">Import Expense</button>';
        html += '</div>';
        html += '<table class="table table-bordered" id="fee_student_statment">';
        html += '<thead>';
        html += '<tr>';
        html += '<th># <input type="checkbox"  name="selectAll" onclick="check_statement_all(this)" id="selectAll" class="check"></th>';
        html += '<th>Student Name</th>';
        html += '<th>Class Section</th>';
        html += '<th>Admission No</th>';
        html += '<th>Total Fees </th>';
        html += '<th>Concession/Discount</th>';
        html += '<th>Opening Balance</th>';
        html += '<th>Total Amount Payable ('+currenct_year+')</th>';
        html += '<th>Amount Received</th>';
        html += '<th>Other Expenses</th>';

        html += '<th>Expenses</th>';
        html += '<th>Excess Amount</th>';
        html += '<th>Balance fee due amount for ('+currenct_year+')</th>';
        html += '<th>Fee for the AY ('+(nextAcadeYear)+')</th>';
        html += '<th>Fee received for the AY ('+(nextAcadeYear)+')</th>';
        html += '<th>Balance amount Payable for the year ('+(nextAcadeYear)+')</th>';
        html += '</thead>';
        html += '</table>';
        $('.stdudentData').html(html);
    }

    function check_statement_all(check){
        if($(check).is(':checked')) {
            $(".checkbox_invoice_state").prop('checked', true);
        }else {
            $(".checkbox_invoice_state").prop('checked', false);
        }
    }


    // var currenct_year = '<?php echo $this->acad_year->getAcadYear() ?>';
    // var invoicePermission = '<?php echo $this->authorization->isAuthorized('FEESV2.INVOICE_GENERATE') ?>';
    // var statementPermission = '<?php echo $this->authorization->isAuthorized('FEESV2.STATEMENT_GENERATE') ?>';
    // function constructFeeHeader(yearDisplay) {
     
    //     var html = '';
    //     html += '<table class="table table-bordered" id="fee_students_invoice">';
    //     html += '<thead>';
    //     html += '<tr>';
    //     html += '<th rowspan="2">Student Name</th>';
    //     html += '<th rowspan="2">Class Section</th>';
    //     html += '<th rowspan="2">Admission No</th>';
    //     for (let index = 0; index < yearDisplay.length; index++) {
    //         html += '<th colspan="5" style="text-align: center;">Fees '+yearDisplay[index].yearname+'</th>';
    //     }
      
    //     html += '<th rowspan="2">Excess</th>';
    //     html += '<th rowspan="2">Closing Balance ('+currenct_year+')</th>';
    //     html += '<th rowspan="2">Total Payable</th>';
    //     html += '<th colspan="2">Invoice ('+currenct_year+') </th>';
    //     html += '<th colspan="2">Statement ('+currenct_year+')</th>';
    //     html += '</tr>';
    //     for (let index = 0; index < yearDisplay.length; index++) {
    //         html += '<th>Total </th>';
    //         html += '<th>Received</th>';
    //         html += '<th>Concession</th>';
    //         html += '<th>Balance</th>';
    //         html += '<th>Discount</th>';
    //     }
    //     if(invoicePermission){
    //         html += '<th><input type="checkbox"  name="selectAll" onclick="check_all_invoice_generate(this)" id="selectAll" class="check"><button type="button" onclick="generate_invoice_pdf_data()" id="bulk_generate_invoice" style="margin-left: 10px;" class="btn btn-primary btn-sm">Generate</button></th></th>';
    //         html += '<th><input type="checkbox"  name="selectAll" onclick="check_all_invoice_email(this)" id="selectAll" class="check"><button type="button" onclick="send_invoice_data_parent()" id="bulk_generate_email" style="margin-left: 10px;" class="btn btn-danger btn-sm">Sent Email</button></th></th></th>';
    //     }
        
    //     if(statementPermission){
    //         html += '<th><input type="checkbox"  name="selectAll" onclick="check_all_statement_generate(this)" id="selectAll" class="check"><button type="button" onclick="generate_statement_pdf_data()" id="bulk_generate_statement" style="margin-left: 10px;" class="btn btn-primary btn-sm">Generate</button></th></th>';
    //         html += '<th><input type="checkbox"  name="selectAll" onclick="check_all_statement_email(this)" id="selectAll" class="check"><button type="button"onclick="send_statement_data_parent()"  style="margin-left: 10px;" id="bulk_generate_email_statement" class="btn btn-danger btn-sm">Sent Email</button></th></th></th>';
    //     }
        
    //     html += '</thead>';
    //     html += '</table>';
    //     $('.stdudentData').html(html);
    // }

    function generate_invoice_pdf_data() {
        var checked_invoice_stds = [];
        $('.check_invoice_pdf:checked').each(function(){
            checked_invoice_stds.push($(this).val());
        });
        if(checked_invoice_stds.length > 0){
            $('#bulk_generate_invoice').html('Generating..');
            for (let istd = 0; istd < checked_invoice_stds.length; istd++) {
               generate_invoice_bulk(checked_invoice_stds[istd]);
            }
        }
    }

    // function generate_statement_pdf_data() {
    //     var checked_statement_stds = [];
    //     $('.check_statement_pdf:checked').each(function(){
    //         checked_statement_stds.push($(this).val());
    //     });
    //     if(checked_statement_stds.length > 0){
    //         $('#bulk_generate_statement').html('Generating..');
    //         for (let istd = 0; istd < checked_statement_stds.length; istd++) {
    //            generate_statement_bulk(checked_statement_stds[istd]);
    //         }
    //     }
    // }

    function send_invoice_data_parent() {
        var checked_invoice_stds_email = [];
        $('.check_invoice_email:not([disabled]):checked').each(function(){
            checked_invoice_stds_email.push($(this).val());
        });
        if(checked_invoice_stds_email.length > 0){
            $('#bulk_generate_email').html('Generating..');
            for (let istd = 0; istd < checked_invoice_stds_email.length; istd++) {
                send_fee_invoice_bulk(checked_invoice_stds_email[istd], 'Send Email', 'Invoice');
            }
        }
    }

    function send_fee_invoice_bulk(student_admission_id, transverse, invoice_type) {
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/send_fee_invoice_to_parent'); ?>',
            type: 'post',
            data: {'student_admission_id':student_admission_id,'transverse':transverse,'invoice_type':invoice_type},
            success: function(data) {
                var resData = data.trim();
                if (resData == 'Email template not found') {
                    $(function(){
                        new PNotify({
                            title: 'Error',
                            text:  resData,
                            type: 'error',
                        });
                    });
                }else if(resData == 'PDF Not Generated'){
                    $(function(){
                        new PNotify({
                            title: 'Error',
                            text:  resData,
                            type: 'error',
                        });
                    });
                }else{
                    if (resData) {
                        $('#send_success'+student_admission_id).show();
                    }else{
                        $(function(){
                            new PNotify({
                            title: 'Error',
                            text:  'Something went wrong',
                            type: 'error',
                            });
                        });
                    }
                }
            }
        });
    }

    function generate_invoice_bulk(student_id) {
        var school = '<?php echo $this->settings->getSetting('school_short_name') ?>';
        if (school == 'iish') {
            url = '<?php echo site_url('feesv2/fees_student/generate_invoice_details_iish') ?>';
        }else{
            url = '<?php echo site_url('feesv2/fees_student/generate_invoice_details') ?>';
        }
        $.ajax({
            url:url,
            type:'post',
            data:{'student_id':student_id},
            success:function(data){
                if (data != 0) {
                    waitTimer = setInterval(function() {check_pdf_generated_invoice(student_id)}, 5000);
                }else{
                    $(function(){
                        new PNotify({
                        title: 'Error',
                        text:  'Invoice template not added',
                        type: 'error',
                        });
                    });
                }
            }
        });
    }

    function check_pdf_generated_invoice(student_id) {
        $.ajax({
            url:'<?php echo site_url('feesv2/fees_student/check_pdf_generated_invoice') ?>',
            type:'post',
            data:{'student_id':student_id,'invoice_type':'Invoice'},
            success:function(data){
                var res = data.trim();
                if(res) {
                    clearInterval(waitTimer);
                    $('#downloadInvoice'+student_id).show();
                    $('#invoice_email'+student_id).prop('checked', true);
                }
            }
        });
    }

    function generate_statement_bulk(student_id) {
        $.ajax({
            url:'<?php echo site_url('feesv2/fees_student/generate_statement_details') ?>',
            type:'post',
            data:{'student_id':student_id},
            success:function(data){
                if (data != 0) {
                    waitTimer = setInterval(function() {check_pdf_generated_statement(student_id)}, 5000);
                }else{
                    $(function(){
                        new PNotify({
                        title: 'Error',
                        text:  'Statement template not added',
                        type: 'error',
                        });
                    });
                }
            }
        });
    }

    function check_pdf_generated_statement(student_id) {
        $.ajax({
        url:'<?php echo site_url('feesv2/fees_student/check_pdf_generated_invoice') ?>',
        type:'post',
        data:{'student_id':student_id,'invoice_type':'Statement'},
        success:function(data){
            var res = data.trim();
            if(res) {
                clearInterval(waitTimer);
                $('#downloadStatement'+student_id).show();
                // $('#statement_email'+student_id).prop('checked', true);
            }
        }
        });
    }

    var current_acad_year  ='<?php echo $this->acad_year->getAcadYearId() ?>';
    var acadyearSetting = '<?php echo $this->settings->getSetting('academic_year_id') ?>'; 
  function prepare_invoice_statement_student_table(stdData, index, yearDisplay) {
    var srNo = index * 100;
    var html = '';
    html += '<tbody>';
    for (var i = 0; i < stdData.length; i++) {
        let fees = stdData[i].fees;
        html += '<tr>';
        html += '<td>'+stdData[i].stdName+'</td>';
        html += '<td>'+stdData[i].className+''+stdData[i].section_name+'</td>';
        html += '<td>'+stdData[i].admission_no+'</td>';
        var totalExcess = 0;
        var TotalpayableAmount = 0;
        for (var f = 0; f < fees.length; f++) {
            var discountBlueprint = 0;
            var totalFeeAmount = 0;
            var totalFeeAmountPaid = 0;
            var totalConcession = 0;
            for(var fd in fees[f].discounts){
                if(fees[f].discounts[fd].discount == undefined){
                    discountBlueprint = 0;
                }else{
                    discountBlueprint += parseFloat(fees[f].discounts[fd].discount);
                }
            }

            html += '<td class="detailed" style="display:none">';
            for(var fd in fees[f].fee_data){
                if(fees[f].fee_data[fd].length > 0){
                    for (let m = 0; m < fees[f].fee_data[fd].length; m++) {
                        totalFeeAmount += parseFloat(fees[f].fee_data[fd][m].component_amount);
                        totalFeeAmountPaid += parseFloat(fees[f].fee_data[fd][m].component_paid);
                        html += fees[f].fee_data[fd][m].blueprint_name +': <b>'+fees[f].fee_data[fd][m].component_amount+'</b><br>';
                    }
                }else{
                    html += '-';
                }
            }
            html += '</td>';

            html += '<td class="detailed" style="display:none">';
            for(var frc in fees[f].fee_data){
                if(fees[f].fee_data[frc].length > 0){
                    for (let n = 0; n < fees[f].fee_data[frc].length; n++) {
                        html += fees[f].fee_data[frc][n].blueprint_name +': <b>'+fees[f].fee_data[frc][n].component_paid+'</b><br>';
                    }
                }else{
                    html += '-';
                }
            }
            html += '</td>';

            html += '<td class="detailed" style="display:none">';
            for(var fd in fees[f].con_data){
                if(fees[f].con_data[fd].length > 0){
                    for (let m = 0; m < fees[f].con_data[fd].length; m++) {
                        totalConcession += parseFloat(fees[f].con_data[fd][m].concession_amount);
                        html += fees[f].con_data[fd][m].feev2_predefined_name +': <b>'+fees[f].con_data[fd][m].concession_amount+'</b><br>';
                    }
                }
            }
            html += '</td>';
            html += '<td class="detailed" style="display:none">'+(totalFeeAmount - totalFeeAmountPaid - totalConcession)+'</td>';

            var balance = totalFeeAmount - totalFeeAmountPaid - totalConcession;
            TotalpayableAmount += balance;
            html += '<td class="summary">'+numberToCurrency(totalFeeAmount)+'</td>';
            html += '<td class="summary">'+numberToCurrency((totalFeeAmountPaid - discountBlueprint))+'</td>';
            html += '<td class="summary">'+numberToCurrency(totalConcession)+'</td>';
            html += '<td class="summary">'+numberToCurrency(balance)+'</td>';
            html += '<td>'+numberToCurrency(discountBlueprint)+'</td>';
        }


       
        var excess = stdData[i].excess_amount.toFixed(2);
        var closingbal = stdData[i].closing_balance.toFixed(2);
       
        var excess_balance = 0;
        if (current_acad_year != acadyearSetting) {
            excess_balance = parseFloat(excess);
        }

        var closing_balance = 0;
        if (current_acad_year != acadyearSetting) {
            closing_balance = parseFloat(closingbal);
        }

        var closingTotal = parseFloat(excess_balance - closing_balance);
        
        totalExcess += parseFloat(excess_balance);
        html += '<td><b>'+numberToCurrency(excess_balance)+'</b></td>';

        if(closingTotal > 0){
            var feeCurrentYearBal = '<strong>('+numberToCurrency(closingTotal)+')</strong>';
        }else{
            var feeCurrentYearBal = '<strong>'+numberToCurrency(Math.abs(closingTotal))+'</strong>';
        }

        html += '<td>'+feeCurrentYearBal+'</td>';

        var payableAmount = TotalpayableAmount;
        var displayPayableAmount = numberToCurrency(payableAmount);
        if(payableAmount < 0){
            displayPayableAmount = '('+numberToCurrency(Math.abs(payableAmount))+')';
        }
        html += '<td>'+displayPayableAmount+'</td>';


        var downloadShow = 'display:none';
        var invoiceEmail = 'disabled';
        var emailSend = 'display:none';

        var downloadShowStatement = 'display:none';
        var statementEmail = 'disabled';
        var emailSendstatement = 'display:none';

        if(stdData[i].invoice.pdf_status == 1){
            downloadShow = 'display:';
            invoiceEmail = '';
        }

        if(stdData[i].statment.pdf_status == 1){
            downloadShowStatement = 'display:';
            statementEmail = '';
        }
        
        if(stdData[i].statment !=''){
            if(stdData[i].statment.email_master_id !=''){
                emailSendstatement = '';
            }
        }

        if(invoicePermission){
            html += '<td><input type="checkbox" class="check_invoice_pdf" value="'+stdData[i].std_admission_id+'"><a id="downloadInvoice'+stdData[i].std_admission_id+'" onclick="download_invoice('+stdData[i].std_admission_id+',\''+'Invoice'+'\')" style="float: right;'+downloadShow+'" class="btn btn-info btn-sm show_invoice">Download <span class="fa fa-cloud-download"></span></a></td>';

            html += '<td><input type="checkbox" id="invoice_email'+stdData[i].std_admission_id+'" class="check_invoice_email" '+invoiceEmail+' value="'+stdData[i].std_admission_id+'"><span id="send_success'+stdData[i].std_admission_id+'" style="'+emailSend+'"> Sent</span></td>';
        }
       
        if(statementPermission){
            html += '<td><input type="checkbox" class="check_statement_pdf" value="'+stdData[i].std_admission_id+'"><a id="downloadStatement'+stdData[i].std_admission_id+'" onclick="download_invoice('+stdData[i].std_admission_id+',\''+'Statement'+'\')" style="float: right;'+downloadShowStatement+'" class="btn btn-info btn-sm show_statement">Download <span class="fa fa-cloud-download"></span></a></td>';

            html += '<td><input type="checkbox" id="statement_email'+stdData[i].std_admission_id+'"  class="check_statement_email" '+statementEmail+' value="'+stdData[i].std_admission_id+'"><span id="send_success_statement'+stdData[i].std_admission_id+'" style="'+emailSendstatement+'"> Sent</span></td>';
        }
        html += '</tr>';
        
    }
    html += '</tbody>';
    $('#fee_students_invoice').append(html);
    index++;
    report_index(index);
  }

  function download_invoice(student_id, invoice_type) {
    window.location.href='<?php echo site_url('feesv2/fees_student/download_invoice/') ?>'+student_id+'/'+invoice_type;
  }

    function check_all_invoice_generate(check){
        if($(check).is(':checked')) {
            $(".check_invoice_pdf").prop('checked', true);
        }
        else {
            $(".check_invoice_pdf").prop('checked', false);
        }
    }

    function check_all_invoice_email(check) {
        if($(check).is(':checked')) {
            $(".check_invoice_email").prop('checked', true);
        }
        else {
            $(".check_invoice_email").prop('checked', false);
        }
    }

    function check_all_statement_generate(check) {
        if($(check).is(':checked')) {
            $(".check_statement_pdf").prop('checked', true);
        }
        else {
            $(".check_statement_pdf").prop('checked', false);
        }
    }

    function check_all_statement_email(check) {
        if($(check).is(':checked')) {
            $(".check_statement_email").prop('checked', true);
        }
        else {
            $(".check_statement_email").prop('checked', false);
        }
    }
 
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }

</script>

<style>
.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2 !important;
		border: 1px solid #ccc !important;
		border-radius: 4px !important;
		margin-right: 5px !important;
	}
.dt-button-collection .buttons-print{
  padding: 0.5em 1em !important;
}

.dt-button-collection .buttons-excel{
  padding: 0.5em 1em !important;
}

.dataTables_wrapper .dataTables_filter input{
    line-height: 2.3;
    padding:0px 10px;
  }
  .glyphicon-print:before{
    margin-right: 0.6rem;
  }

  .dt-button-collection .buttons-excel .fa-file-excel-o:before {
    margin-right: 0.6rem;
  }

  div.dt-button-background{
    background:none;
    z-index: 0;
  }
  div.dt-buttons>.dt-button, div.dt-buttons>div.dt-button-split .dt-button{
    border-radius: 8px !important;
    line-height: 20px;
  }
  .btn-primary{
    border-radius: 8px !important; 
  }
  .btn-info{
    border-radius: 8px !important; 
  }
  #fee_student_statment_filter .dt-button-collection{
    height: 300px;
    overflow: scroll;
  }
.dataTables_wrapper .dataTables_filter input{
    line-height: 2.3;
    padding:0px 10px;
  }
  
  #daily_dataTable_length{
    width: 100%;
    display: contents;
  }
.dataTables_filter input {
    background-color: #f2f2f2 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    margin-right: 5px !important;
}
</style>
